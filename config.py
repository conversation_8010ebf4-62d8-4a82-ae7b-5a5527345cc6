# -*- coding: utf-8 -*-
"""
视频压缩配置文件
"""

# 压缩设置
COMPRESSION_SETTINGS = {
    # 目标文件大小（MB）
    'target_size_mb': 5.0,
    
    # 输入目录（相对于脚本位置）
    'input_directory': '视频',
    
    # 输出目录（None表示在输入目录下创建compressed文件夹）
    'output_directory': None,
    
    # 是否先测试一个文件
    'test_first': True,
    
    # 支持的视频格式
    'supported_formats': ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'],
    
    # FFmpeg编码参数
    'encoding_params': {
        # 视频编码器
        'video_codec': 'libx264',
        
        # 编码速度预设 (ultrafast, superfast, veryfast, faster, fast, medium, slow, slower, veryslow)
        'preset': 'medium',
        
        # 质量参数 (0-51, 数值越小质量越好，推荐18-28)
        'crf': 28,
        
        # 音频编码器
        'audio_codec': 'aac',
        
        # 音频码率
        'audio_bitrate': '128k',
        
        # 音频声道数
        'audio_channels': 2
    },
    
    # 分辨率缩放设置
    'resolution_scaling': {
        # 根据码率自动调整分辨率
        'auto_scale': True,
        
        # 码率阈值和对应的缩放比例
        'scale_thresholds': {
            500: 0.5,   # 码率<500kbps时，缩放到50%
            1000: 0.7,  # 码率<1000kbps时，缩放到70%
            2000: 0.85, # 码率<2000kbps时，缩放到85%
        },
        
        # 固定分辨率（如果不使用自动缩放）
        'fixed_width': None,
        'fixed_height': None
    }
}

# 质量预设
QUALITY_PRESETS = {
    'high_quality': {
        'crf': 23,
        'preset': 'slow',
        'target_size_mb': 8.0
    },
    'balanced': {
        'crf': 28,
        'preset': 'medium',
        'target_size_mb': 5.0
    },
    'high_compression': {
        'crf': 32,
        'preset': 'fast',
        'target_size_mb': 3.0
    }
}
