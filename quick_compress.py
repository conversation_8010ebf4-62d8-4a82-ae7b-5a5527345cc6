#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速视频压缩工具
简化版本，一键压缩所有视频到5MB以内
"""

import os
import sys
from video_compressor import VideoCompressor


def main():
    """快速压缩主函数"""
    print("🚀 快速视频压缩工具")
    print("=" * 40)
    print("目标：将所有视频压缩到5MB以内")
    print("=" * 40)
    
    # 检查输入目录
    input_dir = "视频"
    if not os.path.exists(input_dir):
        print(f"❌ 输入目录不存在: {input_dir}")
        print("请确保'视频'文件夹存在于当前目录")
        input("按回车键退出...")
        return
    
    # 创建压缩器
    compressor = VideoCompressor(target_size_mb=5.0)
    
    # 查找视频文件
    video_files = compressor.find_video_files(input_dir)
    
    if not video_files:
        print("❌ 未找到支持的视频文件")
        input("按回车键退出...")
        return
    
    print(f"📁 找到 {len(video_files)} 个视频文件")
    
    # 显示文件列表
    total_size = 0
    for i, file_path in enumerate(video_files, 1):
        size_mb = os.path.getsize(file_path) / (1024 * 1024)
        total_size += size_mb
        print(f"  {i}. {os.path.basename(file_path)} ({size_mb:.1f} MB)")
    
    print(f"\n📊 总大小: {total_size:.1f} MB")
    print(f"🎯 预计压缩后: ~{len(video_files) * 5:.1f} MB")
    
    # 确认开始
    print("\n" + "="*40)
    choice = input("开始压缩所有文件？(y/n): ").lower().strip()
    if choice not in ['y', 'yes', '是']:
        print("已取消")
        return
    
    # 开始压缩
    try:
        compressor.batch_compress(
            input_directory=input_dir,
            output_directory=None,
            test_first=False  # 直接批量压缩，不测试
        )
    except KeyboardInterrupt:
        print("\n⚠️  用户中断了压缩过程")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
    
    print("\n✅ 压缩完成！")
    print("📁 压缩后的文件保存在: 视频/compressed/")
    input("\n按回车键退出...")


if __name__ == "__main__":
    main()
