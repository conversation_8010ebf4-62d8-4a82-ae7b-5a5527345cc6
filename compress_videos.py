#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频批量压缩主程序
使用配置文件进行个性化设置
"""

import os
import sys
from video_compressor import VideoCompressor
from config import COMPRESSION_SETTINGS, QUALITY_PRESETS


def select_quality_preset():
    """选择质量预设"""
    print("\n请选择压缩质量预设:")
    print("1. 高质量 (目标8MB, 质量较好)")
    print("2. 平衡模式 (目标5MB, 质量中等) [推荐]")
    print("3. 高压缩 (目标3MB, 质量较低)")
    print("4. 使用自定义设置")
    
    while True:
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == '1':
            return QUALITY_PRESETS['high_quality']
        elif choice == '2':
            return QUALITY_PRESETS['balanced']
        elif choice == '3':
            return QUALITY_PRESETS['high_compression']
        elif choice == '4':
            return None
        else:
            print("请输入有效选项 (1-4)")


def get_custom_settings():
    """获取自定义设置"""
    print("\n自定义设置:")
    
    while True:
        try:
            target_size = float(input("目标文件大小 (MB, 默认5.0): ") or "5.0")
            if target_size > 0:
                break
            else:
                print("请输入大于0的数值")
        except ValueError:
            print("请输入有效的数值")
    
    return {'target_size_mb': target_size}


def main():
    """主函数"""
    print("🎬 视频批量压缩工具 v1.0")
    print("=" * 60)
    
    # 检查输入目录
    input_dir = COMPRESSION_SETTINGS['input_directory']
    if not os.path.exists(input_dir):
        print(f"❌ 输入目录不存在: {input_dir}")
        print("请确保'视频'文件夹存在于当前目录")
        return
    
    # 选择质量预设
    preset = select_quality_preset()
    
    if preset:
        target_size = preset['target_size_mb']
        print(f"\n✅ 已选择预设，目标大小: {target_size} MB")
    else:
        custom_settings = get_custom_settings()
        target_size = custom_settings['target_size_mb']
        print(f"\n✅ 使用自定义设置，目标大小: {target_size} MB")
    
    # 显示当前设置
    print(f"\n📁 输入目录: {input_dir}")
    print(f"🎯 目标大小: {target_size} MB")
    print(f"🧪 测试模式: {'开启' if COMPRESSION_SETTINGS['test_first'] else '关闭'}")
    
    # 确认开始
    print(f"\n{'='*60}")
    confirm = input("确认开始压缩？(y/n): ").lower().strip()
    if confirm not in ['y', 'yes', '是']:
        print("已取消压缩")
        return
    
    # 创建压缩器并开始处理
    compressor = VideoCompressor(target_size_mb=target_size)
    
    try:
        compressor.batch_compress(
            input_directory=input_dir,
            output_directory=COMPRESSION_SETTINGS['output_directory'],
            test_first=COMPRESSION_SETTINGS['test_first']
        )
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断了压缩过程")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
    
    print("\n程序结束")


if __name__ == "__main__":
    main()
