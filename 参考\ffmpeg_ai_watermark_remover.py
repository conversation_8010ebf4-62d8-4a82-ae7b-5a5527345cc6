#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
FFmpeg AI水印去除模块
使用IOPaint + FFmpeg实现，完全保持原始视频质量
修复羽化蒙版逻辑，确保水印完全去除
"""

import os
import sys
import numpy as np
import subprocess
import time
import tempfile
import shutil
import json
from PIL import Image, ImageFilter, ImageDraw
from typing import List, Dict, Optional, Tuple

class FFmpegAIWatermarkRemover:
    """FFmpeg AI水印去除器（高质量版本）"""
    
    def __init__(self, temp_dir: Optional[str] = None, enable_gpu: bool = True, iopaint_lock=None, config_manager=None):
        """
        初始化FFmpeg AI水印去除器

        Args:
            temp_dir: 临时目录路径，如果为None则自动创建
            enable_gpu: 是否启用GPU加速
            iopaint_lock: IOPaint并发控制锁
            config_manager: 配置管理器，用于获取羽化参数
        """
        if temp_dir is None:
            self.temp_dir = tempfile.mkdtemp(prefix="ffmpeg_ai_watermark_")
            self._auto_cleanup = True
        else:
            self.temp_dir = temp_dir
            self._auto_cleanup = False
            os.makedirs(self.temp_dir, exist_ok=True)

        # 检测最佳设备（根据配置决定是否使用GPU）
        self.enable_gpu = enable_gpu
        self.device = self._detect_best_device()

        # IOPaint并发控制锁
        self.iopaint_lock = iopaint_lock

        # 配置管理器和羽化参数
        self.config_manager = config_manager
        self.feather_radius = self.config_manager.get("feather_radius", 3) if self.config_manager else 3
        self.feather_sigma = self.config_manager.get("feather_sigma", 1.0) if self.config_manager else 1.0
        self.edge_blur_radius = self.config_manager.get("edge_blur_radius", 8) if self.config_manager else 8
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self._auto_cleanup:
            self.cleanup()
    
    def cleanup(self):
        """清理临时文件"""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
        except Exception as e:
            print(f"⚠️ 清理临时文件失败: {e}")

    def _detect_best_device(self) -> str:
        """检测最佳设备（根据配置决定是否使用GPU）"""
        if not self.enable_gpu:
            print("💻 GPU加速已禁用，使用CPU进行水印去除")
            return "cpu"

        try:
            import torch
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                gpu_name = torch.cuda.get_device_name(0) if gpu_count > 0 else "Unknown"
                print(f"🚀 检测到GPU: {gpu_name} (共{gpu_count}个)")
                return "cuda"
        except ImportError:
            print("⚠️ PyTorch未安装，无法使用GPU加速")
        except Exception as e:
            print(f"⚠️ GPU检测失败: {e}")

        print("💻 使用CPU进行水印去除")
        return "cpu"

    def _get_ffmpeg_gpu_params(self) -> tuple:
        """获取FFmpeg GPU加速参数"""
        if self.device == "cuda":
            # 检测NVIDIA GPU编码器
            try:
                # 检查是否支持NVENC
                result = subprocess.run(['ffmpeg', '-hide_banner', '-encoders'],
                                      capture_output=True, text=True)
                if 'h264_nvenc' in result.stdout:
                    return ('-c:v', 'h264_nvenc', '-preset', 'fast')
                elif 'nvenc' in result.stdout:
                    return ('-c:v', 'nvenc', '-preset', 'fast')
            except:
                pass

        # 默认使用CPU编码
        return ('-c:v', 'libx264', '-preset', 'fast')
    
    def get_video_info(self, video_path: str) -> Optional[Dict]:
        """使用FFmpeg获取完整视频信息"""
        cmd = ['ffprobe', '-v', 'quiet', '-print_format', 'json', 
               '-show_format', '-show_streams', video_path]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, 
                                  encoding='utf-8', errors='ignore')
            if result.returncode == 0:
                data = json.loads(result.stdout)
                
                video_stream = None
                for stream in data['streams']:
                    if stream['codec_type'] == 'video':
                        video_stream = stream
                        break
                
                if video_stream:
                    # 添加格式信息
                    video_stream['format'] = data.get('format', {})
                
                return video_stream
        except Exception as e:
            print(f"❌ 获取视频信息失败: {e}")
        
        return None
    
    def extract_first_frame_ffmpeg(self, video_path: str) -> Optional[str]:
        """使用FFmpeg提取视频首帧（保持原始质量）"""
        output_path = os.path.join(self.temp_dir, "first_frame.png")
        
        cmd = [
            'ffmpeg', '-y',
            '-i', video_path,
            '-vframes', '1',
            '-q:v', '1',  # 最高质量
            '-pix_fmt', 'rgb24',  # 确保RGB格式
            output_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, 
                                  encoding='utf-8', errors='ignore')
            if result.returncode == 0 and os.path.exists(output_path):
                return output_path
            else:
                return None
        except Exception as e:
            return None
    
    def create_mask_pillow(self, image_path: str, regions: List[Dict]) -> Optional[str]:
        """使用Pillow创建精确的水印区域蒙版"""
        try:
            # 读取图像获取尺寸
            with Image.open(image_path) as img:
                width, height = img.size
            
            # 创建黑色蒙版
            mask = Image.new('L', (width, height), 0)
            draw = ImageDraw.Draw(mask)
            
            # 为每个区域添加白色蒙版
            for region in regions:
                x, y, w, h = region['x'], region['y'], region['width'], region['height']
                draw.rectangle([x, y, x + w, y + h], fill=255)
            
            # 保存蒙版
            mask_path = os.path.join(self.temp_dir, "mask.png")
            mask.save(mask_path)
            return mask_path
            
        except Exception as e:
            return None
    
    def remove_watermark_with_iopaint(self, image_path: str, mask_path: str) -> Optional[str]:
        """使用IOPaint去除水印（支持GPU加速）"""
        # 创建唯一的IOPaint输出目录，避免并发冲突
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        output_dir = os.path.join(self.temp_dir, f"iopaint_output_{unique_id}")
        os.makedirs(output_dir, exist_ok=True)
        print(f"🎨 创建唯一IOPaint输出目录: {output_dir}")

        try:
            # IOPaint批处理命令（使用缓存的设备信息）
            cmd = [
                sys.executable, "-m", "iopaint", "run",
                "--model", "lama",
                "--device", self.device,
                "--image", image_path,
                "--mask", mask_path,
                "--output", output_dir
            ]

            print(f"🎨 使用{self.device.upper()}进行AI水印修复...")

            # 使用锁确保IOPaint串行执行（避免GPU内存冲突）
            if self.iopaint_lock:
                print(f"🔒 等待IOPaint锁...")
                with self.iopaint_lock:
                    print(f"🔓 获得IOPaint锁，开始处理...")
                    result = subprocess.run(cmd, capture_output=True, text=True,
                                          encoding='utf-8', errors='ignore', timeout=300)
                    print(f"🔒 IOPaint处理完成，释放锁")
            else:
                # 执行IOPaint
                result = subprocess.run(cmd, capture_output=True, text=True,
                                      encoding='utf-8', errors='ignore', timeout=300)
            
            if result.returncode == 0:
                # 查找输出文件
                import glob
                possible_outputs = glob.glob(os.path.join(output_dir, "*.png"))
                if possible_outputs:
                    print(f"✅ IOPaint修复成功，输出文件: {possible_outputs[0]}")
                    return possible_outputs[0]
                else:
                    print(f"❌ IOPaint执行成功但未找到输出文件，输出目录: {output_dir}")
            else:
                print(f"❌ IOPaint执行失败，返回码: {result.returncode}")
                print(f"❌ IOPaint错误输出: {result.stderr}")
                print(f"❌ IOPaint标准输出: {result.stdout}")

            return None
                
        except subprocess.TimeoutExpired:
            print(f"❌ IOPaint执行超时（300秒）")
            return None
        except Exception as e:
            print(f"❌ IOPaint执行异常: {e}")
            import traceback
            print(f"❌ 异常详情: {traceback.format_exc()}")
            return None
    
    def create_precise_mask(self, regions: List[Dict], width: int, height: int) -> np.ndarray:
        """创建精确的蒙版（不羽化，确保完全替换）"""
        # 创建蒙版
        mask = np.zeros((height, width), dtype=np.float32)
        
        for region in regions:
            x, y, w, h = region['x'], region['y'], region['width'], region['height']
            # 确保坐标在有效范围内
            x = max(0, min(x, width))
            y = max(0, min(y, height))
            w = min(w, width - x)
            h = min(h, height - y)
            
            # 在水印区域设置为1（完全替换）
            mask[y:y+h, x:x+w] = 1.0
        
        # 只在边缘进行轻微羽化，避免明显边界
        from scipy import ndimage
        try:
            # 使用配置的羽化强度
            mask = ndimage.gaussian_filter(mask, sigma=self.feather_sigma)
        except ImportError:
            # 如果没有scipy，使用Pillow
            mask_pil = Image.fromarray((mask * 255).astype(np.uint8))
            mask_pil = mask_pil.filter(ImageFilter.GaussianBlur(radius=int(self.feather_sigma)))
            mask = np.array(mask_pil).astype(np.float32) / 255.0
        
        # 扩展为3通道
        mask_3d = np.stack([mask, mask, mask], axis=2)
        
        return mask_3d
    
    def apply_repaired_regions(self, original_frame: np.ndarray, 
                             repaired_image: np.ndarray, 
                             mask: np.ndarray) -> np.ndarray:
        """精确应用修复区域到帧"""
        # 确保图像尺寸一致
        if repaired_image.shape[:2] != original_frame.shape[:2]:
            # 使用Pillow调整大小
            repaired_pil = Image.fromarray(repaired_image)
            repaired_pil = repaired_pil.resize((original_frame.shape[1], original_frame.shape[0]), 
                                             Image.Resampling.LANCZOS)
            repaired_image = np.array(repaired_pil)
        
        # 转换为浮点数进行混合
        original = original_frame.astype(np.float32)
        repaired = repaired_image.astype(np.float32)
        
        # 在蒙版区域完全使用修复后的图像，其他区域保持原样
        result = original * (1 - mask) + repaired * mask
        
        # 转换回uint8
        return np.clip(result, 0, 255).astype(np.uint8)
    
    def process_video_with_ffmpeg(self, video_path: str, repaired_image_path: str, 
                                regions: List[Dict], output_path: str) -> bool:
        """使用FFmpeg处理视频（保持原始质量）"""
        try:
            # 读取修复后的图像
            repaired_img = np.array(Image.open(repaired_image_path))
            if repaired_img is None:
                return False
            
            # 获取视频信息
            video_info = self.get_video_info(video_path)
            if not video_info:
                return False
            
            width = int(video_info['width'])
            height = int(video_info['height'])
            
            # 处理帧率（支持分数形式）
            r_frame_rate = video_info.get('r_frame_rate', '30/1')
            if '/' in r_frame_rate:
                num, den = map(int, r_frame_rate.split('/'))
                fps = num / den
            else:
                fps = float(r_frame_rate)
            
            # 获取原始码率
            bit_rate = video_info.get('bit_rate')
            if not bit_rate and 'format' in video_info:
                bit_rate = video_info['format'].get('bit_rate')
            
            # 🚀 优化方案：使用蒙版叠加法，避免逐帧处理
            print("🎯 使用蒙版叠加优化方案（跳过逐帧处理）")

            # 提取首帧用于边缘融合
            first_frame_path = self.extract_first_frame_ffmpeg(video_path)
            if not first_frame_path:
                print("❌ 无法提取视频首帧")
                return False

            # 读取首帧图像
            first_frame = np.array(Image.open(first_frame_path))

            # 创建精确蒙版
            mask = self.create_precise_mask(regions, width, height)

            # 使用蒙版叠加方法处理视频
            return self.process_video_with_mask_overlay(video_path, repaired_img, mask, output_path, video_info, first_frame)
        except Exception as e:
            print(f"❌ FFmpeg视频处理异常: {e}")
            import traceback
            print(f"❌ 异常详情: {traceback.format_exc()}")
            return False

    def merge_audio_high_quality(self, original_video: str, processed_video: str,
                               output_path: str, video_info: Dict) -> bool:
        """高质量音频合并（完全保持原始质量）"""

        # 构建FFmpeg命令
        cmd = [
            'ffmpeg', '-y',
            '-i', processed_video,  # 处理后的视频
            '-i', original_video,   # 原始视频（用于音频）
            '-c:v', 'copy',         # 直接复制视频流（避免重新编码）
            '-c:a', 'copy',         # 直接复制音频流
            '-map', '0:v:0',        # 使用第一个输入的视频
            '-map', '1:a:0',        # 使用第二个输入的音频
            '-avoid_negative_ts', 'make_zero',  # 避免时间戳问题
        ]

        # 保持原始帧率
        r_frame_rate = video_info.get('r_frame_rate')
        if r_frame_rate:
            cmd.extend(['-r', r_frame_rate])

        cmd.append(output_path)

        try:
            print(f"🎬 使用FFmpeg(GPU加速)重新组合视频...")
            result = subprocess.run(cmd, capture_output=True, text=True,
                                  encoding='utf-8', errors='ignore')
            if result.returncode == 0:
                print(f"✅ FFmpeg音频合并成功")
                return True
            else:
                print(f"❌ FFmpeg音频合并失败，返回码: {result.returncode}")
                print(f"❌ FFmpeg错误输出: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ FFmpeg音频合并异常: {e}")
            import traceback
            print(f"❌ 异常详情: {traceback.format_exc()}")
            return False

    def remove_watermarks(self, video_path: str, regions: List[Dict],
                         output_path: str, progress_callback=None) -> bool:
        """
        去除视频水印（FFmpeg高质量方案）

        Args:
            video_path: 输入视频路径
            regions: 水印区域列表，每个区域包含 x, y, width, height
            output_path: 输出视频路径
            progress_callback: 进度回调函数

        Returns:
            bool: 是否成功
        """
        try:
            if progress_callback:
                progress_callback("使用FFmpeg提取视频首帧（高质量）...", 10)

            # 步骤1: 使用FFmpeg提取首帧
            first_frame_path = self.extract_first_frame_ffmpeg(video_path)
            if not first_frame_path:
                return False

            if progress_callback:
                progress_callback("创建精确水印蒙版...", 20)

            # 步骤2: 创建精确蒙版
            mask_path = self.create_mask_pillow(first_frame_path, regions)
            if not mask_path:
                return False

            if progress_callback:
                progress_callback("使用IOPaint AI修复水印...", 30)

            # 步骤3: 使用IOPaint修复
            repaired_path = self.remove_watermark_with_iopaint(first_frame_path, mask_path)

            if not repaired_path:
                if progress_callback:
                    progress_callback("IOPaint修复失败", 40)
                return False

            if progress_callback:
                progress_callback("使用FFmpeg处理视频（保持原始质量）...", 50)

            # 步骤4: 使用FFmpeg处理视频
            success = self.process_video_with_ffmpeg(video_path, repaired_path, regions, output_path)

            if progress_callback:
                progress_callback("完成", 100)

            return success

        except Exception as e:
            return False

    def process_video_with_mask_overlay(self, video_path: str, repaired_img: np.ndarray,
                                       mask: np.ndarray, output_path: str, video_info: dict, first_frame: np.ndarray) -> bool:
        """
        🚀 优化方案：使用蒙版叠加法处理视频，避免逐帧处理

        Args:
            video_path: 输入视频路径
            repaired_img: 修复后的图像
            mask: 水印蒙版
            output_path: 输出视频路径
            video_info: 视频信息
            first_frame: 原始首帧图像（用于边缘融合）

        Returns:
            bool: 处理是否成功
        """
        try:
            print("🎯 开始蒙版叠加处理（超高速模式）")

            # 创建修复蒙版图像
            overlay_path = os.path.join(self.temp_dir, "repair_overlay.png")

            # 创建透明背景的修复叠加层
            height, width = repaired_img.shape[:2]
            overlay_img = np.zeros((height, width, 4), dtype=np.uint8)  # RGBA格式

            # 确保mask是2D的（灰度蒙版）
            if len(mask.shape) == 3:
                # 如果mask是3通道的，取第一个通道
                mask_2d = mask[:, :, 0]
            else:
                # 如果mask已经是2D的，直接使用
                mask_2d = mask

            # 对蒙版进行高级羽化处理，避免硬边缘
            import cv2

            # 自适应羽化半径（根据蒙版大小和配置调整）
            mask_area = np.sum(mask_2d > 0.5)
            base_radius = self.feather_radius
            if mask_area > 50000:  # 大区域
                feather_radius = base_radius + 2
            elif mask_area > 10000:  # 中等区域
                feather_radius = base_radius
            else:  # 小区域
                feather_radius = max(1, base_radius - 1)

            # 多层羽化处理，获得更自然的边缘
            mask_float = mask_2d.astype(np.float32)

            # 第一层：形态学操作，清理边缘噪声
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            mask_float = cv2.morphologyEx(mask_float, cv2.MORPH_CLOSE, kernel)
            mask_float = cv2.morphologyEx(mask_float, cv2.MORPH_OPEN, kernel)

            # 第二层：轻微羽化
            mask_feathered = cv2.GaussianBlur(mask_float, (feather_radius*2+1, feather_radius*2+1), self.feather_sigma)

            # 第三层：边缘软化
            kernel_size = max(3, feather_radius)
            if kernel_size % 2 == 0:
                kernel_size += 1
            mask_feathered = cv2.GaussianBlur(mask_feathered, (kernel_size, kernel_size), self.feather_sigma * 0.5)

            # 第四层：最终边缘平滑，彻底消除硬边缘
            final_blur_size = max(5, feather_radius // 2)
            if final_blur_size % 2 == 0:
                final_blur_size += 1
            mask_feathered = cv2.GaussianBlur(mask_feathered, (final_blur_size, final_blur_size), self.feather_sigma * 0.3)

            # 确保羽化后的值在0-1范围内
            mask_feathered = np.clip(mask_feathered, 0, 1)

            # 应用软阈值，进一步减少边缘残留
            mask_feathered = np.where(mask_feathered < 0.02, 0, mask_feathered)

            print(f"✨ 应用多层羽化处理，羽化半径: {feather_radius}px，蒙版区域: {mask_area}px²")
            print(f"🔍 羽化后蒙版统计: 最小值={mask_feathered.min():.4f}, 最大值={mask_feathered.max():.4f}")
            print(f"🔍 非零像素数量: {np.sum(mask_feathered > 0)}, 边缘像素数量: {np.sum((mask_feathered > 0) & (mask_feathered < 0.98))}")

            # 扩展羽化蒙版到3通道用于RGB处理
            mask_3d = np.stack([mask_feathered] * 3, axis=-1)

            # 高级边缘融合：结合原始图像和修复图像
            # 在蒙版边缘区域进行渐变混合，避免突兀的边界
            original_img = first_frame.astype(np.float32) / 255.0
            repaired_float = repaired_img.astype(np.float32) / 255.0

            # 使用羽化蒙版进行加权混合
            blended_img = original_img * (1 - mask_3d) + repaired_float * mask_3d

            # 创建更精确的Alpha通道，避免黑框问题
            # 只在真正需要修复的区域设置Alpha值
            alpha_channel = np.zeros_like(mask_feathered)

            # 只在蒙版值大于阈值的区域设置Alpha
            threshold = 0.01  # 很小的阈值，避免边缘残留
            alpha_channel[mask_feathered > threshold] = mask_feathered[mask_feathered > threshold]

            # 对Alpha通道进行额外的边缘柔化
            alpha_channel = cv2.GaussianBlur(alpha_channel, (5, 5), 1.0)

            # 转换回uint8并应用到叠加层
            overlay_img[:, :, :3] = (blended_img * 255).astype(np.uint8)  # RGB通道
            overlay_img[:, :, 3] = (alpha_channel * 255).astype(np.uint8)  # 精确的Alpha通道

            print("🎨 应用高级边缘融合和精确Alpha通道，避免黑框问题")

            # 保存叠加层
            from PIL import Image
            overlay_image = Image.fromarray(overlay_img, 'RGBA')
            overlay_image.save(overlay_path)

            print(f"✅ 创建修复叠加层: {overlay_path}")

            # 使用FFmpeg直接叠加蒙版到视频
            return self.apply_overlay_to_video(video_path, overlay_path, output_path, video_info)

        except Exception as e:
            print(f"❌ 蒙版叠加处理失败: {e}")
            import traceback
            print(f"❌ 异常详情: {traceback.format_exc()}")
            return False

    def apply_overlay_to_video(self, video_path: str, overlay_path: str,
                              output_path: str, video_info: dict) -> bool:
        """
        使用FFmpeg将叠加层应用到整个视频

        Args:
            video_path: 输入视频路径
            overlay_path: 叠加层图像路径
            output_path: 输出视频路径
            video_info: 视频信息

        Returns:
            bool: 处理是否成功
        """
        try:
            print("🎬 使用FFmpeg叠加蒙版到视频（GPU加速）")

            # 获取GPU加速参数
            gpu_params = self._get_ffmpeg_gpu_params()

            # 构建FFmpeg命令
            cmd = ['ffmpeg', '-y']

            # 添加GPU解码加速
            if self.device == 'cuda':
                cmd.extend(['-hwaccel', 'cuda'])
                print("🚀 使用CUDA硬件加速")

            # 输入文件
            cmd.extend([
                '-i', video_path,      # 原始视频
                '-i', overlay_path,    # 叠加层
            ])

            # 视频滤镜：使用Alpha混合叠加修复区域，避免黑框
            cmd.extend([
                '-filter_complex', '[0:v][1:v]overlay=0:0:format=auto,format=yuv420p',  # Alpha混合叠加
                '-map', '0:a',  # 保持原始音频
            ])

            # GPU编码参数
            cmd.extend(gpu_params)

            # 质量控制
            bit_rate = video_info.get('bit_rate')
            if bit_rate:
                cmd.extend(['-b:v', str(bit_rate)])
            else:
                cmd.extend(['-crf', '15'])  # 高质量

            # 输出文件
            cmd.append(output_path)

            print(f"🎬 执行FFmpeg叠加命令: {' '.join(cmd)}")

            # 执行命令
            result = subprocess.run(cmd, capture_output=True, text=True,
                                  encoding='utf-8', errors='ignore')

            if result.returncode != 0:
                print(f"❌ FFmpeg叠加失败，返回码: {result.returncode}")
                print(f"❌ FFmpeg错误输出: {result.stderr}")
                return False

            print("✅ 蒙版叠加完成！")
            return True

        except Exception as e:
            print(f"❌ FFmpeg叠加异常: {e}")
            import traceback
            print(f"❌ 异常详情: {traceback.format_exc()}")
            return False

# 便捷函数
def remove_video_watermarks_ffmpeg(video_path: str, regions: List[Dict],
                                 output_path: str, progress_callback=None, enable_gpu: bool = True) -> bool:
    """
    便捷函数：FFmpeg高质量去除视频水印（支持GPU加速）

    Args:
        video_path: 输入视频路径
        regions: 水印区域列表，格式: [{'x': int, 'y': int, 'width': int, 'height': int}, ...]
        output_path: 输出视频路径
        progress_callback: 进度回调函数，接收 (message: str, progress: int) 参数
        enable_gpu: 是否启用GPU加速

    Returns:
        bool: 是否成功

    Example:
        regions = [
            {'x': 30, 'y': 27, 'width': 310, 'height': 60},
            {'x': 1574, 'y': 961, 'width': 308, 'height': 97}
        ]
        success = remove_video_watermarks_ffmpeg('input.mp4', regions, 'output.mp4', enable_gpu=True)
    """
    with FFmpegAIWatermarkRemover(enable_gpu=enable_gpu) as remover:
        return remover.remove_watermarks(video_path, regions, output_path, progress_callback)
