#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频批量压缩工具
将大视频文件压缩到指定大小以内
"""

import os
import subprocess
import sys
from pathlib import Path
import time
from typing import List, Tuple

class VideoCompressor:
    def __init__(self, target_size_mb: float = 5.0):
        """
        初始化视频压缩器
        
        Args:
            target_size_mb: 目标文件大小（MB）
        """
        self.target_size_mb = target_size_mb
        self.target_size_bytes = target_size_mb * 1024 * 1024
        self.supported_formats = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'}
        
    def get_video_info(self, video_path: str) -> dict:
        """获取视频信息"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_format',
                '-show_streams', video_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True, encoding='utf-8', errors='ignore')
            import json
            if not result.stdout.strip():
                print("FFprobe返回空结果")
                return None
            info = json.loads(result.stdout)
            
            # 获取视频流信息
            video_stream = None
            for stream in info['streams']:
                if stream['codec_type'] == 'video':
                    video_stream = stream
                    break
            
            if not video_stream:
                return None
                
            duration = float(info['format']['duration'])
            width = int(video_stream['width'])
            height = int(video_stream['height'])
            
            return {
                'duration': duration,
                'width': width,
                'height': height,
                'size_bytes': int(info['format']['size'])
            }
        except Exception as e:
            print(f"获取视频信息失败: {e}")
            return None
    
    def calculate_bitrate(self, duration: float, target_size_bytes: int) -> int:
        """计算目标码率"""
        # 预留20%空间给音频和其他开销
        video_size_bytes = target_size_bytes * 0.8
        # 转换为kbps
        bitrate_kbps = int((video_size_bytes * 8) / (duration * 1000))
        return max(bitrate_kbps, 100)  # 最小码率100kbps
    
    def calculate_resolution(self, width: int, height: int, target_bitrate: int) -> Tuple[int, int]:
        """根据码率计算合适的分辨率"""
        # 如果码率很低，需要降低分辨率
        if target_bitrate < 500:
            # 非常低码率，大幅降低分辨率
            scale_factor = 0.5
        elif target_bitrate < 1000:
            # 低码率，适度降低分辨率
            scale_factor = 0.7
        elif target_bitrate < 2000:
            # 中等码率，轻微降低分辨率
            scale_factor = 0.85
        else:
            # 高码率，保持原分辨率
            scale_factor = 1.0
        
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        
        # 确保宽高是偶数（H.264要求）
        new_width = new_width - (new_width % 2)
        new_height = new_height - (new_height % 2)
        
        return new_width, new_height
    
    def compress_video(self, input_path: str, output_path: str) -> bool:
        """压缩单个视频文件"""
        print(f"\n开始压缩: {os.path.basename(input_path)}")
        
        # 获取视频信息
        video_info = self.get_video_info(input_path)
        if not video_info:
            print("无法获取视频信息，跳过此文件")
            return False
        
        original_size_mb = video_info['size_bytes'] / (1024 * 1024)
        print(f"原始大小: {original_size_mb:.2f} MB")
        print(f"目标大小: {self.target_size_mb} MB")
        
        # 计算压缩参数
        target_bitrate = self.calculate_bitrate(video_info['duration'], self.target_size_bytes)
        new_width, new_height = self.calculate_resolution(
            video_info['width'], video_info['height'], target_bitrate
        )
        
        print(f"目标码率: {target_bitrate} kbps")
        print(f"目标分辨率: {new_width}x{new_height}")
        
        # 构建FFmpeg命令
        cmd = [
            'ffmpeg', '-i', input_path,
            '-c:v', 'libx264',           # 使用H.264编码器
            '-preset', 'medium',          # 编码速度预设
            '-crf', '28',                # 质量参数（18-28，数值越大质量越低）
            '-maxrate', f'{target_bitrate}k',  # 最大码率
            '-bufsize', f'{target_bitrate * 2}k',  # 缓冲区大小
            '-vf', f'scale={new_width}:{new_height}',  # 缩放分辨率
            '-c:a', 'aac',               # 音频编码器
            '-b:a', '128k',              # 音频码率
            '-ac', '2',                  # 双声道
            '-movflags', '+faststart',   # 优化网络播放
            '-y',                        # 覆盖输出文件
            output_path
        ]
        
        try:
            print("正在压缩...")
            start_time = time.time()

            # 执行压缩
            result = subprocess.run(cmd, capture_output=True, text=True, check=True, encoding='utf-8', errors='ignore')
            
            end_time = time.time()
            elapsed_time = end_time - start_time
            
            # 检查输出文件
            if os.path.exists(output_path):
                output_size_mb = os.path.getsize(output_path) / (1024 * 1024)
                compression_ratio = (1 - output_size_mb / original_size_mb) * 100
                
                print(f"压缩完成!")
                print(f"压缩后大小: {output_size_mb:.2f} MB")
                print(f"压缩率: {compression_ratio:.1f}%")
                print(f"耗时: {elapsed_time:.1f} 秒")
                
                if output_size_mb <= self.target_size_mb:
                    print("✅ 达到目标大小")
                else:
                    print("⚠️  未达到目标大小，但已尽力压缩")
                
                return True
            else:
                print("❌ 压缩失败：输出文件不存在")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"❌ 压缩失败: {e}")
            print(f"错误输出: {e.stderr}")
            return False
    
    def find_video_files(self, directory: str) -> List[str]:
        """查找目录中的所有视频文件"""
        video_files = []
        directory_path = Path(directory)
        
        for file_path in directory_path.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                video_files.append(str(file_path))
        
        return sorted(video_files)
    
    def batch_compress(self, input_directory: str, output_directory: str = None, test_first: bool = True):
        """批量压缩视频文件"""
        if output_directory is None:
            output_directory = os.path.join(input_directory, "compressed")
        
        # 创建输出目录
        os.makedirs(output_directory, exist_ok=True)
        
        # 查找所有视频文件
        video_files = self.find_video_files(input_directory)
        
        if not video_files:
            print("未找到支持的视频文件")
            return
        
        print(f"找到 {len(video_files)} 个视频文件:")
        for i, file_path in enumerate(video_files, 1):
            size_mb = os.path.getsize(file_path) / (1024 * 1024)
            print(f"{i}. {os.path.basename(file_path)} ({size_mb:.2f} MB)")
        
        if test_first and video_files:
            print(f"\n{'='*50}")
            print("🧪 测试模式：先压缩第一个文件")
            print(f"{'='*50}")
            
            test_file = video_files[0]
            test_output = os.path.join(
                output_directory, 
                f"compressed_{os.path.basename(test_file)}"
            )
            
            success = self.compress_video(test_file, test_output)
            
            if success:
                print(f"\n✅ 测试压缩成功！")
                print(f"测试文件保存在: {test_output}")
                
                while True:
                    choice = input("\n是否继续压缩剩余文件？(y/n): ").lower().strip()
                    if choice in ['y', 'yes', '是']:
                        remaining_files = video_files[1:]
                        break
                    elif choice in ['n', 'no', '否']:
                        print("压缩已停止")
                        return
                    else:
                        print("请输入 y 或 n")
            else:
                print("❌ 测试压缩失败，请检查设置")
                return
        else:
            remaining_files = video_files
        
        # 批量压缩
        if remaining_files:
            print(f"\n{'='*50}")
            print(f"🚀 开始批量压缩 {len(remaining_files)} 个文件")
            print(f"{'='*50}")
            
            success_count = 0
            for i, file_path in enumerate(remaining_files, 1):
                print(f"\n进度: {i}/{len(remaining_files)}")
                
                output_path = os.path.join(
                    output_directory,
                    f"compressed_{os.path.basename(file_path)}"
                )
                
                if self.compress_video(file_path, output_path):
                    success_count += 1
            
            print(f"\n{'='*50}")
            print(f"📊 批量压缩完成")
            print(f"成功: {success_count}/{len(remaining_files)}")
            print(f"输出目录: {output_directory}")
            print(f"{'='*50}")


def main():
    """主函数"""
    print("🎬 视频批量压缩工具")
    print("=" * 50)
    
    # 设置参数
    target_size = 5.0  # 目标大小（MB）
    input_dir = "视频"  # 输入目录
    
    # 检查输入目录
    if not os.path.exists(input_dir):
        print(f"❌ 输入目录不存在: {input_dir}")
        return
    
    # 创建压缩器
    compressor = VideoCompressor(target_size_mb=target_size)
    
    # 开始批量压缩
    compressor.batch_compress(input_dir, test_first=True)


if __name__ == "__main__":
    main()
