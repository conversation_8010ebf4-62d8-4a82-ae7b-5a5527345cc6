@echo off
chcp 65001 >nul
title 视频批量压缩工具

echo.
echo ========================================
echo           视频批量压缩工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python
    echo 请先安装Python 3.6或更高版本
    echo.
    pause
    exit /b 1
)

REM 检查FFmpeg是否安装
ffmpeg -version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到FFmpeg
    echo 请先安装FFmpeg并添加到系统PATH
    echo.
    pause
    exit /b 1
)

REM 检查视频文件夹是否存在
if not exist "视频" (
    echo ❌ 错误：未找到"视频"文件夹
    echo 请在当前目录创建"视频"文件夹并放入要压缩的视频文件
    echo.
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo.

REM 运行压缩程序
python quick_compress.py

pause
