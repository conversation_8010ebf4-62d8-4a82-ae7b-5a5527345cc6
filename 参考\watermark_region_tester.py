#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
水印区域测试工具
用于测试和调整水印区域配置
"""

import os
import sys
from PIL import Image, ImageDraw

def test_watermark_regions(image_path, regions, output_path=None):
    """
    测试水印区域配置
    
    Args:
        image_path: 测试图像路径
        regions: 水印区域列表
        output_path: 输出图像路径（可选）
    """
    try:
        # 读取图像
        with Image.open(image_path) as img:
            # 创建副本用于绘制
            test_img = img.copy()
            draw = ImageDraw.Draw(test_img)
            
            # 绘制水印区域
            colors = ['red', 'blue', 'green', 'yellow', 'purple']
            
            for i, region in enumerate(regions):
                x, y, w, h = region['x'], region['y'], region['width'], region['height']
                color = colors[i % len(colors)]
                
                # 绘制矩形框
                draw.rectangle([x, y, x + w, y + h], outline=color, width=3)
                
                # 添加标签
                draw.text((x + 5, y + 5), f"Region {i+1}", fill=color)
                
                print(f"区域 {i+1}: x={x}, y={y}, w={w}, h={h} (颜色: {color})")
            
            # 保存或显示结果
            if output_path:
                test_img.save(output_path)
                print(f"✅ 测试图像已保存到: {output_path}")
            else:
                # 尝试显示图像
                try:
                    test_img.show()
                except Exception:
                    # 如果无法显示，保存到临时文件
                    temp_path = "watermark_regions_test.png"
                    test_img.save(temp_path)
                    print(f"✅ 测试图像已保存到: {temp_path}")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def extract_first_frame_for_test(video_path, output_path=None):
    """
    从视频中提取首帧用于测试
    
    Args:
        video_path: 视频文件路径
        output_path: 输出图像路径（可选）
    """
    import subprocess
    
    if output_path is None:
        output_path = os.path.splitext(video_path)[0] + "_first_frame.png"
    
    try:
        cmd = [
            'ffmpeg', '-y',
            '-i', video_path,
            '-vframes', '1',
            '-q:v', '1',
            '-pix_fmt', 'rgb24',
            output_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, 
                              encoding='utf-8', errors='ignore')
        
        if result.returncode == 0 and os.path.exists(output_path):
            print(f"✅ 首帧已提取到: {output_path}")
            return output_path
        else:
            print(f"❌ 首帧提取失败: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"❌ 首帧提取失败: {e}")
        return None

def main():
    """主函数"""
    print("🎯 水印区域测试工具")
    print("=" * 50)

    # 默认水印区域配置（基于1920x1080）
    default_regions = [
        {"x": 30, "y": 27, "width": 310, "height": 60},    # 左上角AI文字
        {"x": 1574, "y": 961, "width": 308, "height": 97}  # 右下角飞影数字人
    ]

    # 测试视频路径
    test_video = "test/1.mp4"

    if len(sys.argv) > 1:
        test_video = sys.argv[1]

    if not os.path.exists(test_video):
        print(f"❌ 测试视频不存在: {test_video}")
        print("请将测试视频放在 test/1.mp4 或通过命令行参数指定")
        return

    print(f"📹 测试视频: {test_video}")

    # 提取首帧
    first_frame = extract_first_frame_for_test(test_video)
    if not first_frame:
        return

    # 获取视频分辨率
    try:
        from region_scaler import get_video_resolution, auto_scale_regions
        width, height = get_video_resolution(test_video)
        if width and height:
            print(f"📐 视频分辨率: {width}x{height}")

            # 测试基准水印区域
            print(f"\n🔍 测试基准水印区域配置 (1920x1080):")
            test_watermark_regions(first_frame, default_regions, "base_regions_test.png")

            # 自动缩放区域
            scaled_regions = auto_scale_regions(test_video, default_regions)

            # 测试缩放后的水印区域
            print(f"\n🔍 测试自适应缩放后的水印区域配置 ({width}x{height}):")
            success = test_watermark_regions(first_frame, scaled_regions)

            if success:
                print("\n✅ 测试完成！请查看生成的图像，确认水印区域是否准确覆盖水印位置")
                print("\n💡 如果区域不准确，请修改配置文件中的 watermark_regions 设置")
                print("   配置文件位置: config/settings.json")
                print("\n📋 基准配置 (1920x1080):")
                for i, region in enumerate(default_regions):
                    print(f"   区域{i+1}: {region}")

                print(f"\n📋 缩放后配置 ({width}x{height}):")
                for i, region in enumerate(scaled_regions):
                    print(f"   区域{i+1}: {region}")
        else:
            # 无法获取分辨率，使用默认区域
            print(f"\n🔍 测试默认水印区域配置:")
            success = test_watermark_regions(first_frame, default_regions)

            if success:
                print("\n✅ 测试完成！请查看生成的图像，确认水印区域是否准确覆盖水印位置")
                print("\n💡 如果区域不准确，请修改配置文件中的 watermark_regions 设置")
                print("   配置文件位置: config/settings.json")
                print("\n📋 当前配置:")
                for i, region in enumerate(default_regions):
                    print(f"   区域{i+1}: {region}")
    except ImportError:
        # 区域缩放模块未找到，使用默认区域
        print(f"\n⚠️ 区域缩放模块未找到，使用默认区域")
        print(f"\n🔍 测试默认水印区域配置:")
        success = test_watermark_regions(first_frame, default_regions)

        if success:
            print("\n✅ 测试完成！请查看生成的图像，确认水印区域是否准确覆盖水印位置")
            print("\n💡 如果区域不准确，请修改配置文件中的 watermark_regions 设置")
            print("   配置文件位置: config/settings.json")
            print("\n📋 当前配置:")
            for i, region in enumerate(default_regions):
                print(f"   区域{i+1}: {region}")
    except Exception as e:
        print(f"\n❌ 区域缩放失败: {e}")
        print(f"\n🔍 使用默认水印区域配置:")
        success = test_watermark_regions(first_frame, default_regions)

    # 清理临时文件
    try:
        if os.path.exists(first_frame):
            os.remove(first_frame)
    except:
        pass

if __name__ == "__main__":
    main()
