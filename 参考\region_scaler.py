#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
水印区域自适应缩放工具
支持不同分辨率视频的水印区域自动转换
"""

import math
from typing import List, Dict, Tuple

class WatermarkRegionScaler:
    """水印区域缩放器"""
    
    # 基准分辨率（配置文件中的水印区域基于此分辨率）
    BASE_WIDTH = 1920
    BASE_HEIGHT = 1080
    BASE_ASPECT_RATIO = BASE_WIDTH / BASE_HEIGHT
    
    def __init__(self):
        """初始化缩放器"""
        pass
    
    def is_16_9_aspect_ratio(self, width: int, height: int, tolerance: float = 0.02) -> bool:
        """
        检查是否为16:9宽高比
        
        Args:
            width: 视频宽度
            height: 视频高度
            tolerance: 容差范围
            
        Returns:
            bool: 是否为16:9比例
        """
        aspect_ratio = width / height
        target_ratio = 16 / 9
        return abs(aspect_ratio - target_ratio) <= tolerance
    
    def get_scale_factors(self, target_width: int, target_height: int) -> <PERSON><PERSON>[float, float]:
        """
        计算缩放因子
        
        Args:
            target_width: 目标视频宽度
            target_height: 目标视频高度
            
        Returns:
            Tuple[float, float]: (x轴缩放因子, y轴缩放因子)
        """
        scale_x = target_width / self.BASE_WIDTH
        scale_y = target_height / self.BASE_HEIGHT
        return scale_x, scale_y
    
    def scale_region(self, region: Dict, scale_x: float, scale_y: float) -> Dict:
        """
        缩放单个水印区域
        
        Args:
            region: 原始区域 {'x': int, 'y': int, 'width': int, 'height': int}
            scale_x: x轴缩放因子
            scale_y: y轴缩放因子
            
        Returns:
            Dict: 缩放后的区域
        """
        return {
            'x': int(round(region['x'] * scale_x)),
            'y': int(round(region['y'] * scale_y)),
            'width': int(round(region['width'] * scale_x)),
            'height': int(round(region['height'] * scale_y))
        }
    
    def scale_regions_for_resolution(self, regions: List[Dict], target_width: int, target_height: int) -> List[Dict]:
        """
        为指定分辨率缩放水印区域列表
        
        Args:
            regions: 原始区域列表（基于1920x1080）
            target_width: 目标视频宽度
            target_height: 目标视频高度
            
        Returns:
            List[Dict]: 缩放后的区域列表
        """
        # 检查是否为16:9比例
        if not self.is_16_9_aspect_ratio(target_width, target_height):
            print(f"⚠️ 警告: 视频分辨率 {target_width}x{target_height} 不是16:9比例，可能影响水印去除效果")
        
        # 如果就是基准分辨率，直接返回
        if target_width == self.BASE_WIDTH and target_height == self.BASE_HEIGHT:
            return regions.copy()
        
        # 计算缩放因子
        scale_x, scale_y = self.get_scale_factors(target_width, target_height)
        
        # 缩放所有区域
        scaled_regions = []
        for i, region in enumerate(regions):
            scaled_region = self.scale_region(region, scale_x, scale_y)
            scaled_regions.append(scaled_region)
            
            print(f"区域{i+1} 缩放: {region} -> {scaled_region} (缩放因子: {scale_x:.3f}, {scale_y:.3f})")
        
        return scaled_regions
    
    def get_common_resolutions_mapping(self, regions: List[Dict]) -> Dict[str, List[Dict]]:
        """
        获取常见分辨率的水印区域映射
        
        Args:
            regions: 基准区域列表（1920x1080）
            
        Returns:
            Dict: 分辨率到区域列表的映射
        """
        common_resolutions = [
            ("1920x1080", 1920, 1080),  # 1080p
            ("1280x720", 1280, 720),    # 720p
            ("1366x768", 1366, 768),    # 768p
            ("1600x900", 1600, 900),    # 900p
            ("2560x1440", 2560, 1440),  # 1440p
            ("3840x2160", 3840, 2160),  # 4K
        ]
        
        mapping = {}
        for res_name, width, height in common_resolutions:
            if self.is_16_9_aspect_ratio(width, height):
                scaled_regions = self.scale_regions_for_resolution(regions, width, height)
                mapping[res_name] = scaled_regions
            else:
                print(f"⚠️ 跳过非16:9分辨率: {res_name}")
        
        return mapping

def get_video_resolution(video_path: str) -> Tuple[int, int]:
    """
    获取视频分辨率
    
    Args:
        video_path: 视频文件路径
        
    Returns:
        Tuple[int, int]: (宽度, 高度)
    """
    import subprocess
    import json
    
    try:
        cmd = ['ffprobe', '-v', 'quiet', '-print_format', 'json', 
               '-show_streams', video_path]
        
        result = subprocess.run(cmd, capture_output=True, text=True, 
                              encoding='utf-8', errors='ignore')
        
        if result.returncode == 0:
            data = json.loads(result.stdout)
            
            for stream in data['streams']:
                if stream['codec_type'] == 'video':
                    width = int(stream['width'])
                    height = int(stream['height'])
                    return width, height
        
        return None, None
        
    except Exception as e:
        print(f"❌ 获取视频分辨率失败: {e}")
        return None, None

def scale_watermark_regions_for_video(video_path: str, base_regions: List[Dict]) -> List[Dict]:
    """
    为指定视频自动缩放水印区域
    
    Args:
        video_path: 视频文件路径
        base_regions: 基准水印区域（1920x1080）
        
    Returns:
        List[Dict]: 适配该视频分辨率的水印区域
    """
    # 获取视频分辨率
    width, height = get_video_resolution(video_path)
    
    if width is None or height is None:
        print(f"❌ 无法获取视频分辨率，使用原始区域: {video_path}")
        return base_regions
    
    print(f"📹 视频分辨率: {width}x{height}")
    
    # 创建缩放器并缩放区域
    scaler = WatermarkRegionScaler()
    scaled_regions = scaler.scale_regions_for_resolution(base_regions, width, height)
    
    return scaled_regions

# 便捷函数
def auto_scale_regions(video_path: str, base_regions: List[Dict]) -> List[Dict]:
    """
    自动缩放水印区域的便捷函数
    
    Args:
        video_path: 视频文件路径
        base_regions: 基准水印区域
        
    Returns:
        List[Dict]: 缩放后的水印区域
    """
    return scale_watermark_regions_for_video(video_path, base_regions)
