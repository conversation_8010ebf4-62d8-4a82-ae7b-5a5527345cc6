# 视频批量压缩工具

一个基于 FFmpeg 和 Python 的视频批量压缩工具，可以将大视频文件压缩到指定大小以内。

## 功能特点

- 🎯 **精确控制文件大小**：可设置目标文件大小（如5MB以内）
- 🔍 **智能参数调整**：根据目标大小自动计算最佳码率和分辨率
- 🧪 **测试模式**：先压缩一个文件测试效果，满意后再批量处理
- 📁 **批量处理**：自动扫描文件夹及子文件夹中的所有视频文件
- 🎨 **多种质量预设**：高质量、平衡、高压缩三种预设可选
- 📊 **详细统计**：显示压缩进度、文件大小变化、压缩率等信息
- 🔧 **灵活配置**：支持自定义压缩参数

## 系统要求

- Python 3.6+
- FFmpeg（已安装并可在命令行中使用）

## 文件结构

```
compress/
├── video_compressor.py    # 核心压缩类
├── compress_videos.py     # 主程序入口
├── config.py             # 配置文件
├── README.md             # 说明文档
└── 视频/                 # 视频文件目录
    ├── 成片/
    └── 样片/
```

## 使用方法

### 1. 快速开始

直接运行主程序：

```bash
python compress_videos.py
```

程序会引导你选择质量预设：
- **高质量**：目标8MB，质量较好
- **平衡模式**：目标5MB，质量中等（推荐）
- **高压缩**：目标3MB，质量较低
- **自定义**：手动设置目标大小

### 2. 工作流程

1. **扫描文件**：程序自动扫描`视频`文件夹中的所有视频文件
2. **测试压缩**：先压缩第一个文件进行测试
3. **确认继续**：查看测试结果，决定是否继续批量处理
4. **批量压缩**：压缩剩余所有文件
5. **查看结果**：在`视频/compressed`文件夹中查看压缩后的文件

### 3. 支持的视频格式

- MP4 (.mp4)
- AVI (.avi)
- MOV (.mov)
- MKV (.mkv)
- WMV (.wmv)
- FLV (.flv)
- WebM (.webm)
- M4V (.m4v)

## 压缩原理

### 智能参数调整

程序会根据目标文件大小自动调整以下参数：

1. **码率计算**：根据视频时长和目标大小计算最佳码率
2. **分辨率缩放**：低码率时自动降低分辨率以保证质量
3. **编码优化**：使用H.264编码器和优化参数

### 分辨率缩放策略

- 码率 < 500kbps：缩放到50%
- 码率 < 1000kbps：缩放到70%
- 码率 < 2000kbps：缩放到85%
- 码率 ≥ 2000kbps：保持原分辨率

## 配置说明

可以通过修改 `config.py` 文件来自定义压缩参数：

```python
COMPRESSION_SETTINGS = {
    'target_size_mb': 5.0,        # 目标文件大小
    'input_directory': '视频',     # 输入目录
    'test_first': True,           # 是否先测试
    # ... 更多设置
}
```

### 高级参数

- **CRF值**：控制视频质量（18-28推荐，数值越小质量越好）
- **编码预设**：控制编码速度（fast/medium/slow）
- **音频设置**：音频码率和声道数

## 使用示例

### 示例1：压缩单个文件夹

```bash
python compress_videos.py
# 选择预设 -> 确认设置 -> 开始压缩
```

### 示例2：自定义目标大小

运行程序后选择"自定义设置"，输入目标大小（如3MB）。

## 注意事项

1. **备份原文件**：程序不会删除原文件，压缩后的文件保存在`compressed`文件夹
2. **质量权衡**：压缩比越大，视频质量损失越明显
3. **处理时间**：压缩时间取决于视频大小和编码设置
4. **磁盘空间**：确保有足够空间存储压缩后的文件

## 故障排除

### 常见问题

1. **FFmpeg未找到**
   ```
   解决：确保FFmpeg已安装并添加到系统PATH
   ```

2. **压缩失败**
   ```
   解决：检查视频文件是否损坏，尝试降低目标大小
   ```

3. **质量不满意**
   ```
   解决：调整CRF值或选择更大的目标文件大小
   ```

## 技术细节

- **视频编码**：H.264 (libx264)
- **音频编码**：AAC
- **容器格式**：MP4
- **质量控制**：CRF + 码率限制
- **优化选项**：faststart（优化网络播放）

## 更新日志

### v1.0
- 初始版本
- 支持批量压缩
- 智能参数调整
- 测试模式
- 多种质量预设
